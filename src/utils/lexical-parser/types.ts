/**
 * TypeScript interfaces for Ghost's Lexical format and parser system
 */

// Base Lexical interfaces
export interface LexicalDocument {
  root: LexicalRootNode;
}

export interface LexicalRootNode {
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
  type: 'root';
  version?: number;
}

export interface LexicalNode {
  type: string;
  version?: number;
  [key: string]: any;
}

// Specific Lexical node types
export interface LexicalParagraphNode extends LexicalNode {
  type: 'paragraph';
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

export interface LexicalHeadingNode extends LexicalNode {
  type: 'heading';
  tag: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

export interface LexicalTextNode extends LexicalNode {
  type: 'text';
  text: string;
  detail?: number;
  format?: number;
  mode?: 'normal' | 'token' | 'segmented';
  style?: string;
}

export interface LexicalListNode extends LexicalNode {
  type: 'list';
  listType: 'bullet' | 'number';
  start?: number;
  tag: 'ul' | 'ol';
  children: LexicalListItemNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

export interface LexicalListItemNode extends LexicalNode {
  type: 'listitem';
  value?: number;
  checked?: boolean;
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

export interface LexicalLinkNode extends LexicalNode {
  type: 'link';
  url: string;
  title?: string;
  rel?: string;
  target?: string;
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

export interface LexicalCodeNode extends LexicalNode {
  type: 'code';
  language?: string;
  code: string;
}

export interface LexicalCodeBlockNode extends LexicalNode {
  type: 'codeblock';
  language?: string;
  code: string;
}

// Ghost-specific card nodes
export interface LexicalMarkdownNode extends LexicalNode {
  type: 'markdown';
  markdown: string;
}

export interface LexicalCalloutNode extends LexicalNode {
  type: 'callout';
  calloutType: string;
  backgroundColor?: string;
  children: LexicalNode[];
}

export interface LexicalBookmarkNode extends LexicalNode {
  type: 'bookmark';
  url: string;
  title?: string;
  description?: string;
  icon?: string;
  thumbnail?: string;
  author?: string;
  publisher?: string;
}

export interface LexicalGalleryNode extends LexicalNode {
  type: 'gallery';
  images: Array<{
    src: string;
    alt?: string;
    caption?: string;
    width?: number;
    height?: number;
  }>;
}

export interface LexicalEmbedNode extends LexicalNode {
  type: 'embed';
  url: string;
  html?: string;
  caption?: string;
  metadata?: {
    title?: string;
    description?: string;
    author?: string;
    publisher?: string;
    thumbnail?: string;
  };
}

export interface LexicalImageNode extends LexicalNode {
  type: 'image';
  src: string;
  alt?: string;
  title?: string;
  width?: number;
  height?: number;
  caption?: string;
}

export interface LexicalTableNode extends LexicalNode {
  type: 'table';
  children: LexicalTableRowNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

export interface LexicalTableRowNode extends LexicalNode {
  type: 'tablerow';
  children: LexicalTableCellNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

export interface LexicalTableCellNode extends LexicalNode {
  type: 'tablecell';
  headerType?: 'row' | 'column' | 'both' | null;
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

// Markdown AST interfaces for conversion
export interface MarkdownAST {
  type: 'root';
  children: MarkdownNode[];
}

export interface MarkdownNode {
  type: string;
  value?: string;
  children?: MarkdownNode[];
  [key: string]: any;
}

export interface MarkdownParagraphNode extends MarkdownNode {
  type: 'paragraph';
  children: MarkdownNode[];
}

export interface MarkdownHeadingNode extends MarkdownNode {
  type: 'heading';
  depth: 1 | 2 | 3 | 4 | 5 | 6;
  children: MarkdownNode[];
}

export interface MarkdownTextNode extends MarkdownNode {
  type: 'text';
  value: string;
}

export interface MarkdownListNode extends MarkdownNode {
  type: 'list';
  ordered: boolean;
  start?: number;
  spread?: boolean;
  children: MarkdownListItemNode[];
}

export interface MarkdownListItemNode extends MarkdownNode {
  type: 'listItem';
  checked?: boolean;
  spread?: boolean;
  children: MarkdownNode[];
}

export interface MarkdownLinkNode extends MarkdownNode {
  type: 'link';
  url: string;
  title?: string;
  children: MarkdownNode[];
}

export interface MarkdownCodeNode extends MarkdownNode {
  type: 'inlineCode';
  value: string;
}

export interface MarkdownCodeBlockNode extends MarkdownNode {
  type: 'code';
  lang?: string;
  value: string;
}

export interface MarkdownImageNode extends MarkdownNode {
  type: 'image';
  url: string;
  alt?: string;
  title?: string;
}

export interface MarkdownTableNode extends MarkdownNode {
  type: 'table';
  align?: ('left' | 'right' | 'center' | null)[];
  children: MarkdownTableRowNode[];
}

export interface MarkdownTableRowNode extends MarkdownNode {
  type: 'tableRow';
  children: MarkdownTableCellNode[];
}

export interface MarkdownTableCellNode extends MarkdownNode {
  type: 'tableCell';
  children: MarkdownNode[];
}

// Conversion context and options
export interface ConversionContext {
  isMarkdownToLexical: boolean;
  preserveUnknownNodes: boolean;
  enableGhostFeatures: boolean;
  fallbackToHTML: boolean;
}

export interface ConversionOptions {
  context?: Partial<Omit<ConversionContext, 'isMarkdownToLexical'>>;
  customConverters?: Map<string, NodeConverter>;
}

// Node converter interface
export abstract class NodeConverter {
  abstract canHandle(nodeType: string): boolean;
  abstract markdownToLexical(node: MarkdownNode, context?: ConversionContext): LexicalNode | LexicalNode[];
  abstract lexicalToMarkdown(node: LexicalNode, context?: ConversionContext): string;
}

// Parser result types
export interface ParseResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  errorCode?: string;
  errorStack?: string;
  warnings?: string[];
}

export type MarkdownToLexicalResult = ParseResult<LexicalDocument>;
export type LexicalToMarkdownResult = ParseResult<string>;

// Text formatting constants
export const TEXT_FORMAT = {
  BOLD: 1,
  ITALIC: 2,
  STRIKETHROUGH: 4,
  UNDERLINE: 8,
  CODE: 16,
  SUBSCRIPT: 32,
  SUPERSCRIPT: 64,
} as const;

export type TextFormat = typeof TEXT_FORMAT[keyof typeof TEXT_FORMAT];

// Callout type mappings
export const CALLOUT_TYPES = {
  note: 'ℹ️',
  tip: '💡',
  info: 'ℹ️',
  warning: '⚠️',
  danger: '🚨',
  error: '❌',
  success: '✅',
  question: '❓',
  quote: '💬',
  example: '📝',
} as const;

export type CalloutType = keyof typeof CALLOUT_TYPES;

// Export utility type for all possible Lexical nodes
export type AnyLexicalNode =
  | LexicalParagraphNode
  | LexicalHeadingNode
  | LexicalTextNode
  | LexicalListNode
  | LexicalListItemNode
  | LexicalLinkNode
  | LexicalCodeNode
  | LexicalCodeBlockNode
  | LexicalMarkdownNode
  | LexicalCalloutNode
  | LexicalBookmarkNode
  | LexicalGalleryNode
  | LexicalEmbedNode
  | LexicalImageNode;

// Export utility type for all possible Markdown nodes
export type AnyMarkdownNode =
  | MarkdownParagraphNode
  | MarkdownHeadingNode
  | MarkdownTextNode
  | MarkdownListNode
  | MarkdownListItemNode
  | MarkdownLinkNode
  | MarkdownCodeNode
  | MarkdownCodeBlockNode
  | MarkdownImageNode;
