/**
 * Main parser class for bidirectional Obsidian Markdown ↔ Ghost Lexical conversion
 */

import {
  LexicalDocument,
  LexicalNode,
  MarkdownAST,
  MarkdownNode,
  ConversionContext,
  ConversionOptions,
  MarkdownToLexicalResult,
  LexicalToMarkdownResult,
  NodeConverter,
} from './types';
import { ConverterRegistry } from './converter-registry';
import { MarkdownASTParser } from './utils/markdown-ast';
import { LexicalUtils } from './utils/lexical-utils';
import { initializeLexicalParser } from './index';

/**
 * Performance configuration for the parser
 */
interface PerformanceConfig {
  /** Maximum number of nodes to process in a single batch */
  batchSize: number;
  /** Whether to use string builder for large documents */
  useStringBuilder: boolean;
  /** Threshold for considering a document "large" (in characters) */
  largeDocumentThreshold: number;
  /** Whether to enable converter caching */
  enableConverterCache: boolean;
}

/**
 * String builder for efficient string concatenation
 */
class StringBuilder {
  private parts: string[] = [];
  private length = 0;

  append(str: string): void {
    this.parts.push(str);
    this.length += str.length;
  }

  toString(): string {
    return this.parts.join('');
  }

  getLength(): number {
    return this.length;
  }

  clear(): void {
    this.parts.length = 0;
    this.length = 0;
  }
}

/**
 * Main parser class for converting between Markdown and Lexical formats
 */
export class LexicalMarkdownParser {
  private static initialized = false;
  private static performanceConfig: PerformanceConfig = {
    batchSize: 1000,
    useStringBuilder: true,
    largeDocumentThreshold: 50000, // 50KB
    enableConverterCache: true
  };

  // Converter cache for performance optimization
  private static converterCache = new Map<string, NodeConverter | null>();

  /**
   * Initialize the parser with default converters
   */
  private static initialize(): void {
    if (this.initialized) return;

    // Call the initialization function
    initializeLexicalParser();

    this.initialized = true;
  }

  /**
   * Configure performance settings for the parser
   * @param config - Performance configuration options
   */
  static configurePerformance(config: Partial<PerformanceConfig>): void {
    this.performanceConfig = { ...this.performanceConfig, ...config };

    // Clear converter cache if caching is disabled
    if (!this.performanceConfig.enableConverterCache) {
      this.converterCache.clear();
    }
  }

  /**
   * Get current performance configuration
   * @returns Current performance configuration
   */
  static getPerformanceConfig(): PerformanceConfig {
    return { ...this.performanceConfig };
  }

  /**
   * Clear performance caches
   */
  static clearCaches(): void {
    this.converterCache.clear();
  }

  /**
   * Get cached converter or fetch and cache it
   * @private
   */
  private static getCachedConverter(nodeType: string): NodeConverter | null {
    if (!this.performanceConfig.enableConverterCache) {
      return ConverterRegistry.getConverter(nodeType);
    }

    if (this.converterCache.has(nodeType)) {
      return this.converterCache.get(nodeType) || null;
    }

    const converter = ConverterRegistry.getConverter(nodeType);
    this.converterCache.set(nodeType, converter);
    return converter;
  }

  /**
   * Convert Markdown to Lexical format
   * @param markdown - The markdown content to convert
   * @param options - Conversion options
   * @returns Lexical document or error result
   */
  static markdownToLexical(
    markdown: string,
    options: ConversionOptions = {}
  ): MarkdownToLexicalResult {
    this.initialize();

    try {
      // Enhanced input validation - check null/undefined first
      if (markdown === null || markdown === undefined) {
        return this.createErrorResult('Input cannot be null or undefined', 'NULL_INPUT');
      }

      if (typeof markdown !== 'string') {
        return this.createErrorResult('Input must be a string', 'INVALID_INPUT');
      }

      // Handle empty content
      if (!markdown.trim()) {
        return {
          success: true,
          data: this.createEmptyLexicalDocument(),
          warnings: []
        };
      }

      // Check for extremely large documents
      if (markdown.length > 10 * 1024 * 1024) { // 10MB limit
        return this.createErrorResult('Document too large (>10MB)', 'DOCUMENT_TOO_LARGE');
      }

      // Parse markdown to AST with error recovery
      const ast = this.parseMarkdownWithErrorRecovery(markdown);
      if (!ast) {
        return this.createErrorResult('Failed to parse markdown after error recovery attempts', 'PARSE_FAILED');
      }

      // Create conversion context
      const context: ConversionContext = {
        isMarkdownToLexical: true,
        preserveUnknownNodes: options.context?.preserveUnknownNodes ?? true,
        enableGhostFeatures: options.context?.enableGhostFeatures ?? true,
        fallbackToHTML: options.context?.fallbackToHTML ?? true,
      };

      // Convert AST to Lexical with error handling
      const conversionResult = this.convertASTToLexicalWithErrorHandling(ast, context);

      return {
        success: true,
        data: conversionResult.document,
        warnings: conversionResult.warnings
      };

    } catch (error) {
      return this.createErrorResult(
        error instanceof Error ? error.message : 'Unknown error occurred',
        'UNEXPECTED_ERROR',
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  /**
   * Convert Lexical format to Markdown
   * @param lexicalDoc - The lexical document to convert
   * @param options - Conversion options
   * @returns Markdown string or error result
   */
  static lexicalToMarkdown(
    lexicalDoc: LexicalDocument,
    options: ConversionOptions = {}
  ): LexicalToMarkdownResult {
    this.initialize();

    try {
      // Enhanced input validation - check null/undefined first
      if (lexicalDoc === null || lexicalDoc === undefined) {
        return this.createMarkdownErrorResult('Input cannot be null or undefined', 'NULL_INPUT');
      }

      if (!lexicalDoc || typeof lexicalDoc !== 'object') {
        return this.createMarkdownErrorResult('Input must be a valid Lexical document', 'INVALID_INPUT');
      }

      if (!lexicalDoc.root) {
        return this.createMarkdownErrorResult('Lexical document missing root node', 'MISSING_ROOT');
      }

      if (!Array.isArray(lexicalDoc.root.children)) {
        return this.createMarkdownErrorResult('Root node children must be an array', 'INVALID_CHILDREN');
      }

      // Handle empty document
      if (lexicalDoc.root.children.length === 0) {
        return {
          success: true,
          data: '',
          warnings: []
        };
      }

      // Validate document structure
      const validationResult = this.validateLexicalDocument(lexicalDoc);
      if (!validationResult.isValid) {
        return this.createMarkdownErrorResult(
          `Invalid document structure: ${validationResult.errors.join(', ')}`,
          'INVALID_STRUCTURE'
        );
      }

      // Create conversion context
      const context: ConversionContext = {
        isMarkdownToLexical: false,
        preserveUnknownNodes: options.context?.preserveUnknownNodes ?? true,
        enableGhostFeatures: options.context?.enableGhostFeatures ?? true,
        fallbackToHTML: options.context?.fallbackToHTML ?? true,
      };

      // Convert Lexical to Markdown with error handling
      const conversionResult = this.convertLexicalToMarkdownWithErrorHandling(lexicalDoc, context);

      return {
        success: true,
        data: conversionResult.markdown,
        warnings: conversionResult.warnings
      };

    } catch (error) {
      return this.createMarkdownErrorResult(
        error instanceof Error ? error.message : 'Unknown error occurred',
        'UNEXPECTED_ERROR',
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  /**
   * Convert Markdown AST to Lexical document
   * @private
   */
  private static convertASTToLexical(
    ast: MarkdownAST,
    context: ConversionContext
  ): LexicalDocument {
    const children: LexicalNode[] = [];
    const nodeCount = ast.children.length;

    // Use batch processing for large documents
    if (nodeCount > this.performanceConfig.batchSize) {
      return this.convertASTToLexicalBatched(ast, context);
    }

    // Process nodes sequentially for smaller documents
    for (const node of ast.children) {
      const converted = this.convertMarkdownNodeToLexical(node, context);
      if (Array.isArray(converted)) {
        children.push(...converted);
      } else if (converted) {
        children.push(converted);
      }
    }

    return {
      root: {
        type: 'root',
        children,
        direction: 'ltr',
        format: '',
        indent: 0,
        version: 1
      }
    };
  }

  /**
   * Convert Markdown AST to Lexical document using batch processing
   * @private
   */
  private static convertASTToLexicalBatched(
    ast: MarkdownAST,
    context: ConversionContext
  ): LexicalDocument {
    const children: LexicalNode[] = [];
    const batchSize = this.performanceConfig.batchSize;

    // Process nodes in batches to avoid blocking the main thread
    for (let i = 0; i < ast.children.length; i += batchSize) {
      const batch = ast.children.slice(i, i + batchSize);

      for (const node of batch) {
        const converted = this.convertMarkdownNodeToLexical(node, context);
        if (Array.isArray(converted)) {
          children.push(...converted);
        } else if (converted) {
          children.push(converted);
        }
      }

      // Allow other tasks to run between batches
      if (i + batchSize < ast.children.length) {
        // In a real implementation, you might use setTimeout or requestIdleCallback
        // For now, we'll just continue synchronously
      }
    }

    return {
      root: {
        type: 'root',
        children,
        direction: 'ltr',
        format: '',
        indent: 0,
        version: 1
      }
    };
  }

  /**
   * Convert a single Markdown node to Lexical node(s)
   * @private
   */
  private static convertMarkdownNodeToLexical(
    node: MarkdownNode,
    context: ConversionContext
  ): LexicalNode | LexicalNode[] | null {
    // Use cached converter for better performance
    const converter = this.getCachedConverter(node.type);

    if (converter) {
      try {
        return converter.markdownToLexical(node, context);
      } catch (error) {
        console.warn(`Error converting markdown node ${node.type}:`, error);
      }
    }

    // Fallback handling
    if (context.preserveUnknownNodes) {
      return this.createFallbackLexicalNode(node);
    }

    return null;
  }

  /**
   * Convert Lexical document to Markdown
   * @private
   */
  private static convertLexicalToMarkdown(
    lexicalDoc: LexicalDocument,
    context: ConversionContext
  ): string {
    const nodeCount = lexicalDoc.root.children.length;
    const isLargeDocument = nodeCount > this.performanceConfig.batchSize;

    // Use StringBuilder for large documents to optimize string concatenation
    if (isLargeDocument && this.performanceConfig.useStringBuilder) {
      return this.convertLexicalToMarkdownOptimized(lexicalDoc, context);
    }

    // Use simple array join for smaller documents
    const markdownParts: string[] = [];

    for (const node of lexicalDoc.root.children) {
      const converted = this.convertLexicalNodeToMarkdown(node, context);
      if (converted) {
        markdownParts.push(converted);
      }
    }

    return markdownParts.join('\n\n').trim();
  }

  /**
   * Convert Lexical document to Markdown using optimized string building
   * @private
   */
  private static convertLexicalToMarkdownOptimized(
    lexicalDoc: LexicalDocument,
    context: ConversionContext
  ): string {
    const builder = new StringBuilder();
    let isFirst = true;

    for (const node of lexicalDoc.root.children) {
      const converted = this.convertLexicalNodeToMarkdown(node, context);
      if (converted) {
        if (!isFirst) {
          builder.append('\n\n');
        }
        builder.append(converted);
        isFirst = false;
      }
    }

    return builder.toString().trim();
  }

  /**
   * Convert a single Lexical node to Markdown
   * @private
   */
  private static convertLexicalNodeToMarkdown(
    node: LexicalNode,
    context: ConversionContext
  ): string | null {
    // Use cached converter for better performance
    const converter = this.getCachedConverter(node.type);

    if (converter) {
      try {
        return converter.lexicalToMarkdown(node, context);
      } catch (error) {
        console.warn(`Error converting lexical node ${node.type}:`, error);
      }
    }

    // Fallback handling
    if (context.preserveUnknownNodes) {
      return this.createFallbackMarkdown(node);
    }

    return null;
  }

  /**
   * Get performance metrics for the last conversion
   * @returns Performance metrics object
   */
  static getPerformanceMetrics(): {
    converterCacheSize: number;
    converterCacheHitRate: number;
  } {
    return {
      converterCacheSize: this.converterCache.size,
      converterCacheHitRate: 0 // Would need to track hits/misses to calculate this
    };
  }

  /**
   * Create a standardized error result for markdown conversion
   * @private
   */
  private static createErrorResult(
    message: string,
    code: string,
    stack?: string
  ): MarkdownToLexicalResult {
    return {
      success: false,
      error: message,
      errorCode: code,
      errorStack: stack
    };
  }

  /**
   * Create a standardized error result for lexical conversion
   * @private
   */
  private static createMarkdownErrorResult(
    message: string,
    code: string,
    stack?: string
  ): LexicalToMarkdownResult {
    return {
      success: false,
      error: message,
      errorCode: code,
      errorStack: stack
    };
  }

  /**
   * Parse markdown with error recovery
   * @private
   */
  private static parseMarkdownWithErrorRecovery(markdown: string): MarkdownAST | null {
    try {
      // First attempt: normal parsing
      const ast = MarkdownASTParser.parse(markdown);
      if (ast) {
        return ast;
      }
    } catch (error) {
      console.warn('Initial markdown parsing failed, attempting error recovery:', error);
    }

    try {
      // Second attempt: sanitize input and try again
      const sanitized = this.sanitizeMarkdown(markdown);
      const ast = MarkdownASTParser.parse(sanitized);
      if (ast) {
        return ast;
      }
    } catch (error) {
      console.warn('Sanitized markdown parsing failed:', error);
    }

    try {
      // Third attempt: fallback to plain text
      return {
        type: 'root',
        children: [{
          type: 'paragraph',
          children: [{
            type: 'text',
            value: markdown
          }]
        }]
      };
    } catch (error) {
      console.error('All markdown parsing attempts failed:', error);
      return null;
    }
  }

  /**
   * Sanitize markdown content to remove problematic characters
   * @private
   */
  private static sanitizeMarkdown(markdown: string): string {
    return markdown
      // Remove null bytes
      .replace(/\0/g, '')
      // Normalize line endings
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // Remove excessive whitespace
      .replace(/\n{4,}/g, '\n\n\n')
      // Remove control characters except tabs and newlines
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  }

  /**
   * Convert AST to Lexical with comprehensive error handling
   * @private
   */
  private static convertASTToLexicalWithErrorHandling(
    ast: MarkdownAST,
    context: ConversionContext
  ): { document: LexicalDocument; warnings: string[] } {
    const warnings: string[] = [];
    const children: LexicalNode[] = [];

    for (const node of ast.children) {
      try {
        const converted = this.convertMarkdownNodeToLexical(node, context);
        if (Array.isArray(converted)) {
          children.push(...converted);
        } else if (converted) {
          children.push(converted);
        } else {
          warnings.push(`Failed to convert node of type: ${node.type}`);
        }
      } catch (error) {
        warnings.push(`Error converting node ${node.type}: ${error instanceof Error ? error.message : 'Unknown error'}`);

        // Try to create a fallback node
        try {
          const fallbackNode = this.createFallbackLexicalNode(node);
          if (fallbackNode) {
            children.push(fallbackNode);
          }
        } catch (fallbackError) {
          warnings.push(`Fallback conversion also failed for node ${node.type}`);
        }
      }
    }

    return {
      document: {
        root: {
          type: 'root',
          children,
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      },
      warnings
    };
  }

  /**
   * Convert Lexical to Markdown with comprehensive error handling
   * @private
   */
  private static convertLexicalToMarkdownWithErrorHandling(
    lexicalDoc: LexicalDocument,
    context: ConversionContext
  ): { markdown: string; warnings: string[] } {
    const warnings: string[] = [];
    const markdownParts: string[] = [];

    for (const node of lexicalDoc.root.children) {
      try {
        const converted = this.convertLexicalNodeToMarkdown(node, context);
        if (converted) {
          markdownParts.push(converted);
        } else {
          warnings.push(`Failed to convert lexical node of type: ${node.type}`);
        }
      } catch (error) {
        warnings.push(`Error converting lexical node ${node.type}: ${error instanceof Error ? error.message : 'Unknown error'}`);

        // Try to create a fallback
        try {
          const fallbackMarkdown = this.createFallbackMarkdown(node);
          if (fallbackMarkdown) {
            markdownParts.push(fallbackMarkdown);
          }
        } catch (fallbackError) {
          warnings.push(`Fallback conversion also failed for lexical node ${node.type}`);
        }
      }
    }

    return {
      markdown: markdownParts.join('\n\n').trim(),
      warnings
    };
  }

  /**
   * Validate Lexical document structure
   * @private
   */
  private static validateLexicalDocument(doc: LexicalDocument): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Check root structure
    if (!doc.root) {
      errors.push('Missing root node');
      return { isValid: false, errors };
    }

    if (doc.root.type !== 'root') {
      errors.push('Root node must have type "root"');
    }

    if (!Array.isArray(doc.root.children)) {
      errors.push('Root children must be an array');
      return { isValid: false, errors };
    }

    // Validate children recursively
    this.validateLexicalNodes(doc.root.children, errors, 0);

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate Lexical nodes recursively
   * @private
   */
  private static validateLexicalNodes(
    nodes: LexicalNode[],
    errors: string[],
    depth: number
  ): void {
    if (depth > 50) {
      errors.push('Document structure too deeply nested (>50 levels)');
      return;
    }

    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];

      if (!node || typeof node !== 'object') {
        errors.push(`Invalid node at position ${i}: not an object`);
        continue;
      }

      if (!node.type || typeof node.type !== 'string') {
        errors.push(`Invalid node at position ${i}: missing or invalid type`);
        continue;
      }

      // Validate children if present
      if ('children' in node && Array.isArray(node.children)) {
        this.validateLexicalNodes(node.children as LexicalNode[], errors, depth + 1);
      }
    }
  }

  /**
   * Create an empty Lexical document
   * @private
   */
  private static createEmptyLexicalDocument(): LexicalDocument {
    return {
      root: {
        type: 'root',
        children: [],
        direction: 'ltr',
        format: '',
        indent: 0,
        version: 1
      }
    };
  }

  /**
   * Create a fallback Lexical node for unknown Markdown nodes
   * @private
   */
  private static createFallbackLexicalNode(node: MarkdownNode): LexicalNode {
    // Convert unknown node to markdown card
    const markdown = this.nodeToMarkdownString(node);
    return {
      type: 'markdown',
      markdown,
      version: 1
    };
  }

  /**
   * Create fallback Markdown for unknown Lexical nodes
   * @private
   */
  private static createFallbackMarkdown(node: LexicalNode): string {
    // For unknown nodes, try to extract text content or use HTML fallback
    if (node.type === 'markdown' && 'markdown' in node) {
      return node.markdown as string;
    }

    // Extract text from children if available
    if ('children' in node && Array.isArray(node.children)) {
      const textParts: string[] = [];
      for (const child of node.children) {
        if (child.type === 'text' && 'text' in child) {
          textParts.push(child.text as string);
        }
      }
      if (textParts.length > 0) {
        return textParts.join('');
      }
    }

    // Last resort: return a comment indicating unknown content
    return `<!-- Unknown Lexical node type: ${node.type} -->`;
  }

  /**
   * Convert a Markdown node to its string representation
   * @private
   */
  private static nodeToMarkdownString(node: MarkdownNode): string {
    // This is a simplified implementation
    // In practice, this would use a proper markdown serializer
    if (node.type === 'text' && node.value) {
      return node.value;
    }

    if (node.children) {
      return node.children.map(child => this.nodeToMarkdownString(child)).join('');
    }

    return `<!-- ${node.type} -->`;
  }

  /**
   * Register a custom node converter
   */
  static registerConverter(nodeType: string, converter: NodeConverter): void {
    ConverterRegistry.register(nodeType, converter);
  }

  /**
   * Get all registered converters
   */
  static getRegisteredConverters(): string[] {
    return ConverterRegistry.getRegisteredTypes();
  }
}
