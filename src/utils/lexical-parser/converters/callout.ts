/**
 * Converter for callout nodes between Obsidian and Ghost formats
 */

import {
  <PERSON>deConverter,
  MarkdownNode,
  LexicalNode,
  LexicalCalloutNode,
  ConversionContext,
  CALLOUT_TYPES,
  CalloutType,
} from '../types';
import { LexicalUtils } from '../utils/lexical-utils';
import { ConverterRegistry } from '../converter-registry';

/**
 * <PERSON><PERSON> conversion of Obsidian callouts to Ghost callout cards
 */
export class CalloutConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return nodeType === 'callout' || nodeType === 'blockquote';
  }

  /**
   * Convert a Markdown callout node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    // Check if this is an Obsidian callout
    const calloutInfo = this.parseObsidianCallout(node);

    if (calloutInfo) {
      return this.createLexicalCalloutNode(
        calloutInfo.type,
        calloutInfo.content,
        context
      );
    }

    // Fallback: treat as regular blockquote
    return this.convertBlockquote(node, context);
  }

  /**
   * Convert a Lexical callout node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    const calloutNode = node as LexicalCalloutNode;

    if (!calloutNode.calloutType) {
      console.warn('Callout node missing type, converting to blockquote');
      return this.convertToBlockquote(calloutNode);
    }

    return this.formatObsidianCallout(
      calloutNode.calloutType,
      calloutNode.children || [],
      context
    );
  }

  /**
   * Parse Obsidian callout syntax from markdown node
   * @private
   */
  private parseObsidianCallout(node: MarkdownNode): {
    type: string;
    content: LexicalNode[];
  } | null {
    // Look for Obsidian callout pattern in blockquote
    if (node.type === 'blockquote' && node.children) {
      const firstChild = node.children[0];

      if (firstChild && firstChild.type === 'paragraph' && firstChild.children) {
        const firstInline = firstChild.children[0];

        // Check if it's an inline node with callout content
        if (firstInline && firstInline.type === 'inline' && firstInline.value) {
          const calloutMatch = firstInline.value.match(/^\[!(\w+)\]\s*([\s\S]*)/);

          if (calloutMatch) {
            const [, type, remainingText] = calloutMatch;

            // Create content nodes
            const content: LexicalNode[] = [];

            // Add remaining text from first line
            if (remainingText.trim()) {
              content.push(LexicalUtils.createTextNode(remainingText.trim()));
            }

            // Add remaining children
            if (firstChild.children.length > 1) {
              for (let i = 1; i < firstChild.children.length; i++) {
                const child = firstChild.children[i];
                const converter = ConverterRegistry.getConverter(child.type);
                if (converter) {
                  const converted = converter.markdownToLexical(child);
                  if (Array.isArray(converted)) {
                    content.push(...converted);
                  } else if (converted) {
                    content.push(converted);
                  }
                } else if (child.value) {
                  content.push(LexicalUtils.createTextNode(child.value));
                }
              }
            }

            // Add remaining paragraphs
            for (let i = 1; i < node.children.length; i++) {
              const child = node.children[i];
              const converter = ConverterRegistry.getConverter(child.type);
              if (converter) {
                const converted = converter.markdownToLexical(child);
                if (Array.isArray(converted)) {
                  content.push(...converted);
                } else if (converted) {
                  content.push(converted);
                }
              }
            }

            return { type: type.toLowerCase(), content };
          }
        }
      }
    }

    return null;
  }

  /**
   * Create a Lexical callout node
   * @private
   */
  private createLexicalCalloutNode(
    type: string,
    content: LexicalNode[],
    context?: ConversionContext
  ): LexicalCalloutNode {
    return {
      type: 'callout',
      calloutType: this.normalizeCalloutType(type),
      backgroundColor: this.getCalloutBackgroundColor(type),
      children: content,
      version: 1
    };
  }

  /**
   * Convert regular blockquote to Lexical
   * @private
   */
  private convertBlockquote(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode {
    const children: LexicalNode[] = [];

    if (node.children) {
      for (const child of node.children) {
        const converter = ConverterRegistry.getConverter(child.type);
        if (converter) {
          try {
            const converted = converter.markdownToLexical(child, context);
            if (Array.isArray(converted)) {
              children.push(...converted);
            } else if (converted) {
              children.push(converted);
            }
          } catch (error) {
            console.warn(`Error converting blockquote child ${child.type}:`, error);
            if (child.value) {
              children.push(LexicalUtils.createTextNode(child.value));
            }
          }
        } else if (child.value) {
          children.push(LexicalUtils.createTextNode(child.value));
        }
      }
    }

    // Return as a regular callout with 'quote' type
    return {
      type: 'callout',
      calloutType: 'quote',
      children,
      version: 1
    };
  }

  /**
   * Format Obsidian callout from Lexical node
   * @private
   */
  private formatObsidianCallout(
    type: string,
    children: LexicalNode[],
    context?: ConversionContext
  ): string {
    const parts: string[] = [];

    // Add callout header
    parts.push(`> [!${type}]`);

    // Convert children to markdown
    for (const child of children) {
      const converter = ConverterRegistry.getConverter(child.type);
      if (converter) {
        try {
          const converted = converter.lexicalToMarkdown(child, context);
          if (converted) {
            // Prefix each line with '> ' for blockquote formatting
            const lines = converted.split('\n');
            const quotedLines = lines.map(line =>
              line.trim() ? `> ${line}` : '>'
            );
            parts.push(...quotedLines);
          }
        } catch (error) {
          console.warn(`Error converting callout child ${child.type}:`, error);
          const text = LexicalUtils.extractText(child);
          if (text) {
            parts.push(`> ${text}`);
          }
        }
      } else {
        const text = LexicalUtils.extractText(child);
        if (text) {
          parts.push(`> ${text}`);
        }
      }
    }

    return parts.join('\n');
  }

  /**
   * Convert callout to regular blockquote
   * @private
   */
  private convertToBlockquote(calloutNode: LexicalCalloutNode): string {
    const parts: string[] = [];

    if (calloutNode.children) {
      for (const child of calloutNode.children) {
        const text = LexicalUtils.extractText(child);
        if (text) {
          const lines = text.split('\n');
          const quotedLines = lines.map(line =>
            line.trim() ? `> ${line}` : '>'
          );
          parts.push(...quotedLines);
        }
      }
    }

    return parts.join('\n');
  }

  /**
   * Normalize callout type
   * @private
   */
  private normalizeCalloutType(type: string): string {
    const normalized = type.toLowerCase();

    // Map common aliases
    const typeMap: Record<string, string> = {
      'info': 'note',
      'important': 'warning',
      'caution': 'warning',
      'attention': 'warning',
      'hint': 'tip',
      'success': 'check',
      'check': 'success',
      'done': 'success',
      'fail': 'error',
      'failure': 'error',
      'missing': 'error',
      'bug': 'error',
      'example': 'note',
      'quote': 'quote',
      'cite': 'quote',
    };

    return typeMap[normalized] || normalized;
  }

  /**
   * Get background color for callout type
   * @private
   */
  private getCalloutBackgroundColor(type: string): string | undefined {
    const colorMap: Record<string, string> = {
      'note': '#e3f2fd',
      'tip': '#f3e5f5',
      'info': '#e8f5e8',
      'warning': '#fff3e0',
      'danger': '#ffebee',
      'error': '#ffebee',
      'success': '#e8f5e8',
      'question': '#f3e5f5',
      'quote': '#f5f5f5',
      'example': '#e3f2fd',
    };

    return colorMap[this.normalizeCalloutType(type)];
  }

  /**
   * Parse Obsidian callout from text
   * @param text - Text that may contain callout syntax
   * @returns Parsed callout information or null
   */
  static parseCalloutFromText(text: string): {
    type: string;
    content: string;
    startIndex: number;
    endIndex: number;
  } | null {
    // Look for callout pattern: > [!type] content
    const calloutRegex = /^>\s*\[!(\w+)\]\s*(.*?)(?=\n(?!>)|$)/ms;
    const match = text.match(calloutRegex);

    if (match) {
      const [fullMatch, type, content] = match;
      return {
        type: type.toLowerCase(),
        content: content.trim(),
        startIndex: match.index || 0,
        endIndex: (match.index || 0) + fullMatch.length
      };
    }

    return null;
  }

  /**
   * Get emoji for callout type
   * @param type - Callout type
   * @returns Emoji string
   */
  static getCalloutEmoji(type: string): string {
    const normalizedType = type.toLowerCase() as CalloutType;
    return CALLOUT_TYPES[normalizedType] || '📝';
  }

  /**
   * Get all supported callout types
   * @returns Array of supported callout types
   */
  static getSupportedTypes(): string[] {
    return Object.keys(CALLOUT_TYPES);
  }

  /**
   * Check if a type is a valid callout type
   * @param type - Type to check
   * @returns True if type is valid
   */
  static isValidCalloutType(type: string): boolean {
    return Object.keys(CALLOUT_TYPES).includes(type.toLowerCase());
  }
}
