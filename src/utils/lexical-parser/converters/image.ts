/**
 * Converter for image nodes between Markdown and Lexical formats
 */

import {
  NodeConverter,
  MarkdownNode,
  LexicalNode,
  LexicalImageNode,
  MarkdownImageNode,
  ConversionContext,
} from '../types';

/**
 * Handles conversion of image nodes
 */
export class ImageConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return nodeType === 'image';
  }

  /**
   * Convert a Markdown image node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    const imageNode = node as MarkdownImageNode;
    
    // Validate URL
    if (!imageNode.url) {
      console.warn('Image node missing URL, skipping');
      return this.createFallbackTextNode(imageNode);
    }

    return this.createLexicalImageNode(
      imageNode.url,
      imageNode.alt,
      imageNode.title
    );
  }

  /**
   * Convert a Lexical image node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    const imageNode = node as LexicalImageNode;
    
    if (!imageNode.src) {
      console.warn('Image node missing src, skipping');
      return '';
    }

    return this.formatMarkdownImage(
      imageNode.src,
      imageNode.alt,
      imageNode.title
    );
  }

  /**
   * Create a Lexical image node
   * @private
   */
  private createLexicalImageNode(
    src: string,
    alt?: string,
    title?: string,
    width?: number,
    height?: number,
    caption?: string
  ): LexicalImageNode {
    const imageNode: LexicalImageNode = {
      type: 'image',
      src: this.normalizeImageURL(src),
      version: 1
    };

    if (alt) {
      imageNode.alt = alt;
    }

    if (title) {
      imageNode.title = title;
    }

    if (width) {
      imageNode.width = width;
    }

    if (height) {
      imageNode.height = height;
    }

    if (caption) {
      imageNode.caption = caption;
    }

    return imageNode;
  }

  /**
   * Format image as markdown
   * @private
   */
  private formatMarkdownImage(
    src: string,
    alt?: string,
    title?: string
  ): string {
    const altText = alt || '';
    
    if (title) {
      return `![${altText}](${src} "${title}")`;
    } else {
      return `![${altText}](${src})`;
    }
  }

  /**
   * Create fallback text node for invalid images
   * @private
   */
  private createFallbackTextNode(imageNode: MarkdownImageNode): LexicalNode {
    const text = imageNode.alt || imageNode.url || '[Image]';
    return {
      type: 'text',
      text,
      detail: 0,
      format: 0,
      mode: 'normal',
      style: '',
      version: 1
    };
  }

  /**
   * Normalize image URL
   * @private
   */
  private normalizeImageURL(url: string): string {
    // Trim whitespace
    url = url.trim();

    // Handle relative URLs
    if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
      return url;
    }

    // Handle data URLs
    if (url.startsWith('data:')) {
      return url;
    }

    // Handle absolute URLs
    if (/^https?:\/\//.test(url)) {
      return url;
    }

    // Handle protocol-relative URLs
    if (url.startsWith('//')) {
      return `https:${url}`;
    }

    // Assume relative path if no protocol
    return url;
  }

  /**
   * Parse markdown image syntax
   * @param text - Text that may contain markdown images
   * @returns Array of parsed image information
   */
  static parseMarkdownImages(text: string): Array<{
    start: number;
    end: number;
    alt: string;
    src: string;
    title?: string;
  }> {
    const images: Array<{
      start: number;
      end: number;
      alt: string;
      src: string;
      title?: string;
    }> = [];

    // Regex for markdown images: ![alt](src "title")
    const imageRegex = /!\[([^\]]*)\]\(([^)]+?)(?:\s+"([^"]*)")?\)/g;
    let match;

    while ((match = imageRegex.exec(text)) !== null) {
      const [fullMatch, alt, src, title] = match;
      images.push({
        start: match.index,
        end: match.index + fullMatch.length,
        alt: alt || '',
        src: src.trim(),
        title: title?.trim()
      });
    }

    return images;
  }

  /**
   * Parse reference-style markdown images
   * @param text - Text that may contain reference images
   * @param references - Map of reference definitions
   * @returns Array of parsed image information
   */
  static parseReferenceImages(
    text: string,
    references: Map<string, { url: string; title?: string }>
  ): Array<{
    start: number;
    end: number;
    alt: string;
    src: string;
    title?: string;
  }> {
    const images: Array<{
      start: number;
      end: number;
      alt: string;
      src: string;
      title?: string;
    }> = [];

    // Regex for reference images: ![alt][ref] or ![alt][]
    const refImageRegex = /!\[([^\]]*)\]\[([^\]]*)\]/g;
    let match;

    while ((match = refImageRegex.exec(text)) !== null) {
      const [fullMatch, alt, refKey] = match;
      const key = refKey || alt; // Use alt as key if ref is empty
      const reference = references.get(key.toLowerCase());

      if (reference) {
        images.push({
          start: match.index,
          end: match.index + fullMatch.length,
          alt: alt || '',
          src: reference.url,
          title: reference.title
        });
      }
    }

    return images;
  }

  /**
   * Extract image dimensions from HTML-style attributes
   * @param text - Text that may contain HTML-style image attributes
   * @returns Parsed dimensions or undefined
   */
  static parseImageDimensions(text: string): {
    width?: number;
    height?: number;
  } | undefined {
    const dimensionRegex = /(?:width|height)=["']?(\d+)["']?/gi;
    const dimensions: { width?: number; height?: number } = {};
    let match;

    while ((match = dimensionRegex.exec(text)) !== null) {
      const [fullMatch, value] = match;
      const dimension = fullMatch.toLowerCase().startsWith('width') ? 'width' : 'height';
      dimensions[dimension] = parseInt(value, 10);
    }

    return Object.keys(dimensions).length > 0 ? dimensions : undefined;
  }

  /**
   * Check if URL points to an image file
   * @param url - URL to check
   * @returns True if URL appears to be an image
   */
  static isImageURL(url: string): boolean {
    // Check file extension
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg|bmp|ico)$/i;
    if (imageExtensions.test(url)) {
      return true;
    }

    // Check for data URLs
    if (url.startsWith('data:image/')) {
      return true;
    }

    // Check for common image hosting patterns
    const imageHosts = [
      /^https?:\/\/.*\.imgur\.com/,
      /^https?:\/\/.*\.cloudinary\.com/,
      /^https?:\/\/.*\.unsplash\.com/,
      /^https?:\/\/.*\.pexels\.com/,
      /^https?:\/\/images\./,
      /^https?:\/\/.*\/images\//,
      /^https?:\/\/.*\/img\//,
      /^https?:\/\/.*\/photos\//,
    ];

    return imageHosts.some(pattern => pattern.test(url));
  }

  /**
   * Generate alt text from image filename
   * @param src - Image source URL
   * @returns Generated alt text
   */
  static generateAltText(src: string): string {
    try {
      // Extract filename from URL
      const url = new URL(src);
      const pathname = url.pathname;
      const filename = pathname.split('/').pop() || '';
      
      // Remove extension and convert to readable text
      const nameWithoutExt = filename.replace(/\.[^.]+$/, '');
      const readable = nameWithoutExt
        .replace(/[-_]/g, ' ')
        .replace(/([a-z])([A-Z])/g, '$1 $2')
        .toLowerCase()
        .trim();

      return readable || 'Image';
    } catch {
      // Fallback for invalid URLs
      return 'Image';
    }
  }

  /**
   * Optimize image URL for different contexts
   * @param src - Original image source
   * @param context - Optimization context
   * @returns Optimized image URL
   */
  static optimizeImageURL(
    src: string,
    context: {
      width?: number;
      height?: number;
      quality?: number;
      format?: 'webp' | 'jpg' | 'png';
    } = {}
  ): string {
    // This is a placeholder for image optimization logic
    // In a real implementation, this might:
    // - Add query parameters for image services
    // - Convert to CDN URLs
    // - Apply format conversions
    // - Add responsive image parameters

    return src;
  }
}
